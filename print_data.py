#!/usr/bin/env python3
"""
Script to print all data from the Data directory
"""

import os
import json
import sys

def print_file_data(file_path, max_articles_per_file=5):
    """Print data from a single file with article limit"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            
        # Split by lines and process each JSON object
        lines = content.split('\n')
        articles_printed = 0
        
        print(f"\n{'='*80}")
        print(f"FILE: {file_path}")
        print(f"{'='*80}")
        
        for line_num, line in enumerate(lines, 1):
            if line.strip():
                try:
                    article = json.loads(line)
                    print(f"\nArticle {articles_printed + 1} (Line {line_num}):")
                    print(f"ID: {article.get('id', 'N/A')}")
                    print(f"Title: {article.get('title', 'N/A')}")
                    print(f"URL: {article.get('url', 'N/A')}")
                    
                    # Print first 500 characters of text
                    text = article.get('text', '')
                    if len(text) > 500:
                        print(f"Text: {text[:500]}...")
                    else:
                        print(f"Text: {text}")
                    
                    print("-" * 60)
                    
                    articles_printed += 1
                    if articles_printed >= max_articles_per_file:
                        remaining_lines = len([l for l in lines[line_num:] if l.strip()])
                        if remaining_lines > 0:
                            print(f"... and {remaining_lines} more articles in this file")
                        break
                        
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON on line {line_num}: {e}")
                    
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")

def main():
    data_dir = "Data"
    
    if not os.path.exists(data_dir):
        print(f"Data directory '{data_dir}' not found!")
        return
    
    # Get all subdirectories
    subdirs = []
    for item in os.listdir(data_dir):
        item_path = os.path.join(data_dir, item)
        if os.path.isdir(item_path):
            subdirs.append(item)
    
    subdirs.sort()
    
    print("BENGALI WIKIPEDIA DATA COLLECTION")
    print("=" * 80)
    print(f"Found {len(subdirs)} subdirectories: {', '.join(subdirs)}")
    
    total_files = 0
    total_articles = 0
    
    # Process each subdirectory
    for subdir in subdirs:
        subdir_path = os.path.join(data_dir, subdir)
        files = [f for f in os.listdir(subdir_path) if f.startswith('wiki_')]
        files.sort()
        
        print(f"\n\nSUBDIRECTORY: {subdir}")
        print(f"Files: {len(files)}")
        print("=" * 60)
        
        # Process first few files from each subdirectory
        for i, filename in enumerate(files[:3]):  # Show first 3 files per subdirectory
            file_path = os.path.join(subdir_path, filename)
            print_file_data(file_path, max_articles_per_file=3)
            total_files += 1
            
        if len(files) > 3:
            print(f"\n... and {len(files) - 3} more files in {subdir} subdirectory")
        
        # Count total articles in this subdirectory
        subdir_articles = 0
        for filename in files:
            file_path = os.path.join(subdir_path, filename)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    lines = [l for l in content.split('\n') if l.strip()]
                    subdir_articles += len(lines)
            except:
                pass
        
        total_articles += subdir_articles
        print(f"\nTotal articles in {subdir}: {subdir_articles}")
        total_files += len(files) - 3  # Add remaining files to count
    
    print(f"\n\n{'='*80}")
    print("SUMMARY")
    print(f"{'='*80}")
    print(f"Total subdirectories: {len(subdirs)}")
    print(f"Total files: {sum(len([f for f in os.listdir(os.path.join(data_dir, subdir)) if f.startswith('wiki_')]) for subdir in subdirs)}")
    print(f"Total articles: {total_articles}")
    print(f"Data format: JSON Lines (one JSON object per line)")
    print(f"Content: Bengali Wikipedia articles")

if __name__ == "__main__":
    main()
